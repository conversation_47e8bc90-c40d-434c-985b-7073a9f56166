package com.zrcoding.barbcker.presentation.features.settings

import android.content.Context
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.core.os.LocaleListCompat

actual class Localization(
    private val context: Context
) {
    actual fun changeLanguage(language: String) {
        Log.d("Localization", "Changing language to: $language")
        val appLocale: LocaleListCompat = LocaleListCompat.forLanguageTags(language)
        Log.d("Localization", "Created LocaleListCompat: $appLocale")
        // Call this on the main thread as it may require Activity.restart()
        AppCompatDelegate.setApplicationLocales(appLocale)
        Log.d("Localization", "Applied locales via AppCompatDelegate")
    }

    actual fun getCurrentLanguage(): String {
        val currentLocales = AppCompatDelegate.getApplicationLocales()
        Log.d("Localization", "Getting current language. Locales: $currentLocales")
        return if (currentLocales.isEmpty) {
            Log.d("Localization", "No locales set, defaulting to 'en'")
            "en" // Default to English if no locale is set
        } else {
            val language = currentLocales[0]?.language ?: "en"
            Log.d("Localization", "Current language: $language")
            language
        }
    }
}

@Composable
actual fun rememberLocalization(): Localization {
    val context = LocalContext.current
    return remember { Localization(context) }
}