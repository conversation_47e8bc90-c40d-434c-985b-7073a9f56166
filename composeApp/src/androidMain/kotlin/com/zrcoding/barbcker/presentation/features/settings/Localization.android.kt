package com.zrcoding.barbcker.presentation.features.settings

import android.content.Context
import androidx.appcompat.app.AppCompatDelegate
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.core.os.LocaleListCompat

actual class Localization(
    private val context: Context
) {
    actual fun changeLanguage(language: String) {
        val appLocale: LocaleListCompat = LocaleListCompat.forLanguageTags(language)
        // Call this on the main thread as it may require Activity.restart()
        AppCompatDelegate.setApplicationLocales(appLocale)
    }
}

@Composable
actual fun rememberLocalization(): Localization {
    val context = LocalContext.current
    return remember { Localization(context) }
}