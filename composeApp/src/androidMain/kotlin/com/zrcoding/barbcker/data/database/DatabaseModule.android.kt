package com.zrcoding.barbcker.data.database

import android.content.Context
import androidx.room.Room
import org.koin.core.module.Module
import org.koin.dsl.module

actual val platformModule: Module = module {
    single {
        val appContext: Context = get()
        val dbFile = appContext.getDatabasePath("barbcker.db")
        Room.databaseBuilder<AppDatabase>(
            context = appContext,
            name = dbFile.absolutePath
        )
    }
}