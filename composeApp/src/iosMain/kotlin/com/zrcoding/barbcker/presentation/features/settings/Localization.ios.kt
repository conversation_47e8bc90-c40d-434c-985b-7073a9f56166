package com.zrcoding.barbcker.presentation.features.settings

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import platform.Foundation.NSUserDefaults

actual class Localization {
    actual fun changeLanguage(language: String) {
        NSUserDefaults.standardUserDefaults.setObject(
            arrayListOf(language), "AppleLanguages"
        )
    }
}


@Composable
actual fun rememberLocalization(): Localization = remember { Localization() }