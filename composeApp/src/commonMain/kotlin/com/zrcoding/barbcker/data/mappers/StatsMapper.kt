package com.zrcoding.barbcker.data.mappers

import com.zrcoding.barbcker.data.database.daos_results.BarberRevenueResult
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.models.BarberRevenue

fun BarberRevenueResult.toBarberRevenue(): BarberRevenue {
    return BarberRevenue(
        barber = <PERSON>(
            uuid = barberUuid,
            name = barberName,
            commissionRate = barberCommissionRate,
            phoneNumber = barberPhoneNumber
        ),
        totalHaircuts = totalHaircuts,
        totalRevenue = totalRevenue,
        totalTips = totalTips
    )
}
