package com.zrcoding.barbcker.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import app.cash.paging.PagingSource
import com.zrcoding.barbcker.data.database.entities.HaircutEntity

@Dao
interface HaircutDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOne(barber: HaircutEntity)

    @Query("SELECT * FROM haircuts")
    fun observeAll(): PagingSource<Int, HaircutEntity>
}


