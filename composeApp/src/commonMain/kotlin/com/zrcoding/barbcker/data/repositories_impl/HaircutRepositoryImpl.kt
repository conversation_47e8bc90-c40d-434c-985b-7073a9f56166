package com.zrcoding.barbcker.data.repositories_impl

import app.cash.paging.Pager
import app.cash.paging.PagingConfig
import app.cash.paging.PagingData
import app.cash.paging.map
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.database.entities.HaircutEntity
import com.zrcoding.barbcker.data.mappers.toHaircut
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.models.Haircut
import com.zrcoding.barbcker.domain.repositories.HaircutRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlin.time.Clock
import kotlin.time.ExperimentalTime
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class HaircutRepositoryImpl(
    private val appDatabase: AppDatabase
) : HaircutRepository {

    @OptIn(ExperimentalUuidApi::class, ExperimentalTime::class)
    override suspend fun insert(
        barber: <PERSON>,
        price: Double,
        tip: Double
    ) {
        val haircutEntity = HaircutEntity(
            uuid = Uuid.random().toString(),
            price = price,
            tip = tip,
            commissionRate = barber.commissionRate,
            createdAt = Clock.System.now().toEpochMilliseconds(),
            barberUuid = barber.uuid
        )
        appDatabase.haircutDao().insertOne(barber = haircutEntity)
    }

    override fun observeAll(): Flow<PagingData<Haircut>> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                appDatabase.haircutDao().observeAll()
            }
        ).flow.map { pagingData ->
            pagingData.map { it.toHaircut() }
        }
    }
}