package com.zrcoding.barbcker.data.repositories_impl

import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.mappers.toBarberRevenue
import com.zrcoding.barbcker.domain.models.BarberRevenue
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class StatsRepositoryImpl(
    private val appDatabase: AppDatabase
) : StatsRepository {

    override fun getTotalRevenue(): Flow<Double> {
        return appDatabase.statsDao().getTotalRevenue()
    }

    override fun getBarbersRevenue(): Flow<List<BarberRevenue>> {
        return appDatabase.statsDao().getBarbersRevenue().map { results ->
            results.map { it.toBarberRevenue() }
        }
    }
}