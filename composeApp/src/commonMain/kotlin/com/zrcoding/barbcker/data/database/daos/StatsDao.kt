package com.zrcoding.barbcker.data.database.daos

import androidx.room.Dao
import androidx.room.Query
import com.zrcoding.barbcker.data.database.daos_results.BarberRevenueResult
import kotlinx.coroutines.flow.Flow

@Dao
interface StatsDao {

    /**
     * Get total revenue for the shop owner (sum of all commissions from haircuts)
     * Commission calculation: (price + tip) * (commission_rate / 100.0)
     */
    @Query(
        """
        SELECT COALESCE(SUM((price + tip) * (commission_rate / 100.0)), 0.0)
        FROM haircuts
    """
    )
    fun getTotalRevenue(): Flow<Double>

    /**
     * Get barber revenue statistics including:
     * - Barber information
     * - Total number of haircuts
     * - Total revenue earned by barber (what barber gets after commission)
     * - Total tips received
     *
     * Revenue calculation for barber: (price + tip) * (1 - commission_rate / 100.0)
     */
    @Query(
        """
        SELECT
            b.uuid as barber_uuid,
            b.name as barber_name,
            b.commission_rate as barber_commission_rate,
            b.phone_number as barber_phone_number,
            COALESCE(COUNT(h.uuid), 0) as total_haircuts,
            COALESCE(SUM((h.price + h.tip) * (1 - h.commission_rate / 100.0)), 0.0) as total_revenue,
            COALESCE(SUM(h.tip), 0.0) as total_tips
        FROM barbers b
        LEFT JOIN haircuts h ON b.uuid = h.barber_uuid
        GROUP BY b.uuid, b.name, b.commission_rate, b.phone_number
        ORDER BY total_revenue DESC
    """
    )
    fun getBarbersRevenue(): Flow<List<BarberRevenueResult>>
}