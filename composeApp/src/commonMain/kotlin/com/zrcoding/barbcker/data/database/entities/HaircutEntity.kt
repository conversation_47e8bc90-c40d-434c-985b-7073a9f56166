package com.zrcoding.barbcker.data.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "haircuts")
data class HaircutEntity(
    @PrimaryKey val uuid: String,
    @ColumnInfo(name = "price") val price: Double,
    @ColumnInfo(name = "tip") val tip: Double,
    @ColumnInfo(name = "commission_rate") val commissionRate: Int,
    @ColumnInfo(name = "created_at") val createdAt: Long,
    @ColumnInfo(name = "barber_uuid") val barberUuid: String
)
