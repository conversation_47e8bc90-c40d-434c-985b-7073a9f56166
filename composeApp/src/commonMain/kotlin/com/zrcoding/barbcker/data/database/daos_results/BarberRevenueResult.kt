package com.zrcoding.barbcker.data.database.daos_results

import androidx.room.ColumnInfo

/**
 * Data class to hold the result from the barber revenue query
 */
data class BarberRevenueResult(
    @ColumnInfo("barber_uuid") val barberUuid: String,
    @ColumnInfo("barber_name") val barberName: String,
    @ColumnInfo("barber_commission_rate") val barberCommissionRate: Int,
    @ColumnInfo("barber_phone_number") val barberPhoneNumber: String,
    @ColumnInfo("total_haircuts") val totalHaircuts: Int,
    @ColumnInfo("total_revenue") val totalRevenue: Double,
    @ColumnInfo("total_tips") val totalTips: Double
)