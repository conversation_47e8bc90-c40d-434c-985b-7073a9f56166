package com.zrcoding.barbcker.data.database

import androidx.room.RoomDatabase
import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import org.koin.core.module.Module
import org.koin.dsl.module

val databaseModule = module {
    includes(platformModule)
    single<AppDatabase> {
        val builder = get<RoomDatabase.Builder<AppDatabase>>()
        return@single builder.fallbackToDestructiveMigration(true)
            .setDriver(BundledSQLiteDriver())
            .setQueryCoroutineContext(Dispatchers.IO)
            .build()
    }
}

expect val platformModule: Module