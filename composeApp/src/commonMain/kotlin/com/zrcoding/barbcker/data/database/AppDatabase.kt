package com.zrcoding.barbcker.data.database

import androidx.room.ConstructedBy
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.RoomDatabaseConstructor
import com.zrcoding.barbcker.data.database.daos.BarberDao
import com.zrcoding.barbcker.data.database.daos.HaircutDao
import com.zrcoding.barbcker.data.database.daos.StatsDao
import com.zrcoding.barbcker.data.database.entities.BarberEntity
import com.zrcoding.barbcker.data.database.entities.HaircutEntity

@Database(
    entities = [BarberEntity::class, HaircutEntity::class],
    version = 1,
    exportSchema = true
)
@ConstructedBy(AppDatabaseConstructor::class)
abstract class AppDatabase : RoomDatabase(), DB {

    abstract fun barberDao(): BarberDao

    abstract fun haircutDao(): HaircutDao

    abstract fun statsDao(): StatsDao

    override fun clearAllTables() {
        super.clearAllTables()
    }
}

interface DB {
    fun clearAllTables() {}
}

@Suppress("NO_ACTUAL_FOR_EXPECT")
expect object AppDatabaseConstructor : RoomDatabaseConstructor<AppDatabase> {
    override fun initialize(): AppDatabase
}




