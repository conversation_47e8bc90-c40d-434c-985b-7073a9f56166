package com.zrcoding.barbcker.data.dtos

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BarberDto(
    @SerialName("uuid") val uuid: String,
    @SerialName("name") val name: String,
    @SerialName("commission_rate") val commissionRate: Int,
    @SerialName("phone_number") val phoneNumber: String,
    @SerialName("created_at") val createdAt: Long,
    @SerialName("updated_at") val updatedAt: Long,
)