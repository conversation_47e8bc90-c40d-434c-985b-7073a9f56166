package com.zrcoding.barbcker.data.mappers

import com.zrcoding.barbcker.data.database.entities.BarberEntity
import com.zrcoding.barbcker.data.dtos.BarberDto
import com.zrcoding.barbcker.domain.models.Barber
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

fun BarberEntity.toBarber(): <PERSON> {
    return Barber(
        uuid = uuid,
        name = name,
        commissionRate = commissionRate,
        phoneNumber = phoneNumber
    )
}

@OptIn(ExperimentalTime::class)
fun Barber.toBarberEntity(): BarberEntity {
    return BarberEntity(
        uuid = uuid,
        name = name,
        commissionRate = commissionRate,
        phoneNumber = phoneNumber,
        createdAt = Clock.System.now().toEpochMilliseconds(),
        updatedAt = Clock.System.now().toEpochMilliseconds()
    )
}

fun BarberEntity.toBarberDto(): BarberDto {
    return BarberDto(
        uuid = uuid,
        name = name,
        commissionRate = commissionRate,
        phoneNumber = phoneNumber,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

fun BarberDto.toBarberEntity(): BarberEntity {
    return BarberEntity(
        uuid = uuid,
        name = name,
        commissionRate = commissionRate,
        phoneNumber = phoneNumber,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}
