package com.zrcoding.barbcker.presentation.features.barber.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.itemKey
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.img_barber1
import barbcker.composeapp.generated.resources.img_barbers
import barbcker.composeapp.generated.resources.setup_barbers_commission_rate
import barbcker.composeapp.generated.resources.setup_barbers_empty_state_description
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.presentation.common.extension.isEmpty
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@Composable
fun BarbersList(
    lazyPagingItems: LazyPagingItems<Barber>,
    onBarberClicked: (Barber) -> Unit,
    onDeleteBarber: (Barber) -> Unit,
) {
    when {
        lazyPagingItems.isEmpty() -> Column(
            modifier = Modifier.fillMaxSize()
                .padding(vertical = MaterialTheme.dimension.big)
                .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large),
        ) {
            Text(
                text = stringResource(Res.string.setup_barbers_empty_state_description),
                style = MaterialTheme.typography.titleMedium,
            )
            Image(
                modifier = Modifier.fillMaxWidth(),
                painter = painterResource(Res.drawable.img_barbers),
                contentDescription = null,
                contentScale = ContentScale.Crop
            )
        }

        else -> LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(
                start = MaterialTheme.dimension.screenPaddingHorizontal,
                end = MaterialTheme.dimension.screenPaddingHorizontal,
                bottom = MaterialTheme.dimension.extraExtraBig,
                top = MaterialTheme.dimension.large
            )
        ) {
            items(
                count = lazyPagingItems.itemCount,
                key = lazyPagingItems.itemKey { it.uuid }
            ) { index ->
                lazyPagingItems[index]?.let { barber ->
                    BarberItem(
                        name = barber.name,
                        commissionRate = barber.commissionRate,
                        phoneNumber = barber.phoneNumber,
                        onClick = { onBarberClicked(barber) },
                        onDelete = { onDeleteBarber(barber) }
                    )
                }
            }
        }
    }
}

@Composable
fun BarberItem(
    name: String,
    commissionRate: Int,
    phoneNumber: String,
    onClick: () -> Unit,
    onDelete: () -> Unit,
) {
    Row(
        modifier = Modifier
            .clickable(onClick = onClick)
            .padding(
                vertical = MaterialTheme.dimension.medium,
                horizontal = MaterialTheme.dimension.small
            )
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
    ) {
        Image(
            modifier = Modifier.size(56.dp).clip(CircleShape),
            painter = painterResource(Res.drawable.img_barber1),
            contentDescription = "Barber avatar"
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
            ) {
                Text(text = name, style = MaterialTheme.typography.bodyLarge)
                Text(text = phoneNumber, style = MaterialTheme.typography.bodyLarge)
            }
            Text(
                text = stringResource(
                    Res.string.setup_barbers_commission_rate,
                    commissionRate
                ),
                style = MaterialTheme.typography.labelLarge
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        IconButton(
            onClick = onDelete
        ) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error
            )
        }
    }
}