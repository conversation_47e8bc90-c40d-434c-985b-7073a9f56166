package com.zrcoding.barbcker.presentation.features.foryou

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.repositories.StatsRepository
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class ForYouViewModel(
    private val statsRepository: StatsRepository,
    val account: Account.Connected,
) : ViewModel() {

    private val _viewState = MutableStateFlow(ForYouViewState())
    val viewState = _viewState.asStateFlow()

    init {
        viewModelScope.launch {
            combine(
                flow = statsRepository.getTotalRevenue(),
                flow2 = statsRepository.getBarbersRevenue()
            ) { totalRevenue, barbersRevenue ->
                ForYouViewState(
                    totalRevenue = "$totalRevenue ${account.currency?.symbol}",
                    barbersRevenue = barbersRevenue.map {
                        it.toUiModel(account.currency?.symbol)
                    }.toPersistentList()
                )
            }.collectLatest { state ->
                _viewState.update { state }
            }
        }
    }
}