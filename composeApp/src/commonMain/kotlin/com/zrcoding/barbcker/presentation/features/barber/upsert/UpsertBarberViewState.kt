package com.zrcoding.barbcker.presentation.features.barber.upsert

import androidx.compose.runtime.Stable
import org.jetbrains.compose.resources.StringResource

@Stable
data class UpsertBarberViewState(
    val name: String = "",
    val commissionRate: Int? = null,
    val canCommissionRateBeZero: Boolean = false,
    val phoneNumber: String = "",
    val nameError: StringResource? = null,
    val commissionRateError: StringResource? = null,
    val phoneNumberError: StringResource? = null,
    val isSubmitting: Boolean = false,
)

data object UpsertBarberSuccessEvent
