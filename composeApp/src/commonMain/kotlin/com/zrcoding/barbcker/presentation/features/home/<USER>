package com.zrcoding.barbcker.presentation.features.home

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navOptions
import com.zrcoding.barbcker.presentation.navigation.Barbers
import com.zrcoding.barbcker.presentation.navigation.CreateHaircut
import com.zrcoding.barbcker.presentation.navigation.ForYou
import com.zrcoding.barbcker.presentation.navigation.Settings
import com.zrcoding.barbcker.presentation.navigation.Stats
import kotlinx.coroutines.CoroutineScope

@Composable
fun rememberAppState(
    navController: NavHostController = rememberNavController(),
    homeNavController: NavHostController = rememberNavController(),
    coroutineScope: CoroutineScope = rememberCoroutineScope()
): AvitoAppState {
    return remember(navController, homeNavController, coroutineScope) {
        AvitoAppState(
            appNavController = navController,
            homeNavController = homeNavController,
            coroutineScope = coroutineScope
        )
    }
}

@Stable
class AvitoAppState(
    val appNavController: NavHostController,
    val homeNavController: NavHostController,
    coroutineScope: CoroutineScope
) {
    // NAVIGATION
    private val previousDestination = mutableStateOf<NavDestination?>(null)

    val currentDestination: NavDestination?
        @Composable get() {
            // Collect the currentBackStackEntryFlow as a state
            val currentEntry = homeNavController.currentBackStackEntryFlow
                .collectAsState(initial = null)

            // Fallback to previousDestination if currentEntry is null
            return currentEntry.value?.destination.also { destination ->
                if (destination != null) {
                    previousDestination.value = destination
                }
            } ?: previousDestination.value
        }

    val currentTopLevelDestination: TopLevelDestination?
        @Composable get() {
            return TopLevelDestination.entries.firstOrNull { topLevelDestination ->
                currentDestination?.hasRoute(route = topLevelDestination.route) == true
            }
        }

    /**
     * Map of top level destinations to be used in the TopBar and BottomBar. The key is the route.
     */
    val topLevelDestinations: List<TopLevelDestination> = TopLevelDestination.entries

    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        val topLevelNavOptions = navOptions {
            // Pop up to the start destination of the graph to
            // avoid building up a large stack of destinations
            // on the back stack as users select items
            popUpTo(homeNavController.graph.findStartDestination().id) {
                saveState = true
            }
            // Avoid multiple copies of the same destination when
            // reselecting the same item
            launchSingleTop = true
            // Restore state when reselecting a previously selected item
            restoreState = true
        }

        when (topLevelDestination) {
            TopLevelDestination.FOR_YOU -> homeNavController.navigate(ForYou, topLevelNavOptions)
            TopLevelDestination.BARBERS -> homeNavController.navigate(
                Barbers,
                topLevelNavOptions
            )

            TopLevelDestination.STATS -> homeNavController.navigate(
                Stats,
                topLevelNavOptions
            )

            TopLevelDestination.SETTINGS -> homeNavController.navigate(
                Settings,
                topLevelNavOptions
            )
        }
    }

    fun onNavigateToCreateHaircut() {
        appNavController.navigate(CreateHaircut)
    }
}