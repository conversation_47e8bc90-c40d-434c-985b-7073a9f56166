package com.zrcoding.barbcker.presentation.features.home

import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavDestination.Companion.hierarchy
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.bottom_bar_barbers
import barbcker.composeapp.generated.resources.bottom_bar_home
import barbcker.composeapp.generated.resources.bottom_bar_settings
import barbcker.composeapp.generated.resources.bottom_bar_stats
import barbcker.composeapp.generated.resources.ic_home
import barbcker.composeapp.generated.resources.ic_home_fill
import barbcker.composeapp.generated.resources.ic_persons
import barbcker.composeapp.generated.resources.ic_persons_fill
import barbcker.composeapp.generated.resources.ic_report
import barbcker.composeapp.generated.resources.ic_report_fill
import barbcker.composeapp.generated.resources.ic_settings
import barbcker.composeapp.generated.resources.ic_settings_fill
import com.zrcoding.barbcker.presentation.navigation.Barbers
import com.zrcoding.barbcker.presentation.navigation.ForYou
import com.zrcoding.barbcker.presentation.navigation.Settings
import com.zrcoding.barbcker.presentation.navigation.Stats
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource
import kotlin.reflect.KClass

/**
 * Type for the top level destinations in the application. Contains metadata about the destination
 * that is used in the top app bar and common navigation UI.
 *
 * @param selectedIcon The icon to be displayed in the navigation UI when this destination is
 * selected.
 * @param unselectedIcon The icon to be displayed in the navigation UI when this destination is
 * not selected.
 * @param iconTextId Text that to be displayed in the navigation UI.
 * @param titleTextId Text that is displayed on the top app bar.
 * @param route The route to use when navigating to this destination.
 */
enum class TopLevelDestination(
    val selectedIcon: DrawableResource,
    val unselectedIcon: DrawableResource,
    val iconTextId: StringResource,
    val titleTextId: StringResource,
    val route: KClass<*>,
) {
    FOR_YOU(
        selectedIcon = Res.drawable.ic_home_fill,
        unselectedIcon = Res.drawable.ic_home,
        iconTextId = Res.string.bottom_bar_home,
        titleTextId = Res.string.bottom_bar_home,
        route = ForYou::class,
    ),
    BARBERS(
        selectedIcon = Res.drawable.ic_persons_fill,
        unselectedIcon = Res.drawable.ic_persons,
        iconTextId = Res.string.bottom_bar_barbers,
        titleTextId = Res.string.bottom_bar_barbers,
        route = Barbers::class,
    ),
    STATS(
        selectedIcon = Res.drawable.ic_report_fill,
        unselectedIcon = Res.drawable.ic_report,
        iconTextId = Res.string.bottom_bar_stats,
        titleTextId = Res.string.bottom_bar_stats,
        route = Stats::class,
    ),
    SETTINGS(
        selectedIcon = Res.drawable.ic_settings_fill,
        unselectedIcon = Res.drawable.ic_settings,
        iconTextId = Res.string.bottom_bar_settings,
        titleTextId = Res.string.bottom_bar_settings,
        route = Settings::class,
    );

    fun getIcon(selected: Boolean): DrawableResource = if (selected) {
        selectedIcon
    } else unselectedIcon
}

fun NavDestination?.isRouteInHierarchy(
    route: KClass<*>
) = this?.hierarchy?.any { it.hasRoute(route) } ?: false



