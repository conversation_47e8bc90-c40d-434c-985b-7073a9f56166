package com.zrcoding.barbcker.presentation.features.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.ic_currency
import barbcker.composeapp.generated.resources.ic_language
import barbcker.composeapp.generated.resources.settings_currency
import barbcker.composeapp.generated.resources.settings_language
import com.zrcoding.barbcker.presentation.design_system.components.BcSecondaryButton
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingRoute(

) {
    val localization = rememberLocalization()
    var showLanguageChooser by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal)
            .padding(vertical = MaterialTheme.dimension.big),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.bigger)
    ) {
        SettingItem(
            icon = Res.drawable.ic_language,
            name = Res.string.settings_language,
            description = "English",
            onClick = { showLanguageChooser = true }
        )
        SettingItem(
            icon = Res.drawable.ic_currency,
            name = Res.string.settings_currency,
            description = "Dollar $",
            onClick = {

            }
        )
    }
    if (showLanguageChooser) {
        BasicAlertDialog(
            modifier = Modifier
                .background(
                    MaterialTheme.colorScheme.background,
                    MaterialTheme.shapes.large
                )
                .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal)
                .padding(vertical = MaterialTheme.dimension.big),
            onDismissRequest = { showLanguageChooser = false },
        ) {
            Column {
                Text(text = "Choose your language:", style = MaterialTheme.typography.titleMedium)
                Spacer(modifier = Modifier.padding(MaterialTheme.dimension.medium))
                BcSecondaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = "English",
                    onClick = {
                        localization.changeLanguage("en")
                        showLanguageChooser = false
                    }
                )
                BcSecondaryButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = "Français",
                    onClick = {
                        localization.changeLanguage("fr")
                        showLanguageChooser = false
                    }
                )
            }
        }
    }
}

@Composable
private fun SettingItem(
    icon: DrawableResource,
    name: StringResource,
    description: String?,
    onClick: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(MaterialTheme.dimension.small),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(MaterialTheme.dimension.extraBig).background(
                        MaterialTheme.colorScheme.surfaceContainer,
                        MaterialTheme.shapes.small
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimension.bigger),
                    painter = painterResource(icon),
                    contentDescription = null,
                )
            }
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(
                    text = stringResource(name),
                    style = MaterialTheme.typography.bodyLarge,
                )
                description?.let {
                    Text(
                        text = it,
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
            }
        }
        Icon(
            imageVector = Icons.AutoMirrored.Default.ArrowForward,
            contentDescription = null,
        )
    }
}