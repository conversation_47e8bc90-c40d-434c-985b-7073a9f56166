package com.zrcoding.barbcker.presentation.features.barber.list

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PersonAddAlt1
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import app.cash.paging.compose.LazyPagingItems
import app.cash.paging.compose.collectAsLazyPagingItems
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.setup_barbers_add_barber_button
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import com.zrcoding.barbcker.presentation.features.barber.common.BarbersList
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun BarbersListRoute(
    navigateToUpsertBarber: (String?) -> Unit,
    viewModel: BarbersListViewModel = koinViewModel()
) {
    val barbers = viewModel.barbers.collectAsLazyPagingItems()
    BarbersListScreen(
        barbers = barbers,
        onAddBarberClicked = { navigateToUpsertBarber(null) },
        onBarberClicked = { navigateToUpsertBarber(it.uuid) },
        onDeleteBarber = viewModel::onDeleteBarber,
    )
}

@Composable
private fun BarbersListScreen(
    barbers: LazyPagingItems<Barber>,
    onAddBarberClicked: () -> Unit,
    onBarberClicked: (Barber) -> Unit,
    onDeleteBarber: (Barber) -> Unit,
) {
    Box(modifier = Modifier.fillMaxSize()) {
        BarbersList(
            lazyPagingItems = barbers,
            onBarberClicked = onBarberClicked,
            onDeleteBarber = onDeleteBarber,
        )
        BcPrimaryButton(
            modifier = Modifier.align(Alignment.BottomEnd).padding(
                end = MaterialTheme.dimension.screenPaddingHorizontal,
                bottom = MaterialTheme.dimension.screenPaddingHorizontal
            ),
            text = stringResource(Res.string.setup_barbers_add_barber_button),
            leadingIcon = {
                Icon(
                    modifier = Modifier.size(MaterialTheme.dimension.bigger),
                    imageVector = Icons.Filled.PersonAddAlt1,
                    contentDescription = null
                )
            },
            onClick = onAddBarberClicked,
        )
    }
}