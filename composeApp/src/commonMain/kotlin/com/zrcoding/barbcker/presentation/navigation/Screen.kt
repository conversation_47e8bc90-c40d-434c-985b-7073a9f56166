package com.zrcoding.barbcker.presentation.navigation

import kotlinx.serialization.Serializable

@Serializable
data object Onboarding

@Serializable
data object Auth

@Serializable
data object CompleteAccount

@Serializable
data object SetupBarbers

@Serializable
data class UpsertBarber(val barberId: String? = null)

@Serializable
data object Home

@Serializable
data object ForYou

@Serializable
data object Barbers

@Serializable
data object Stats

@Serializable
data object Settings

@Serializable
data object CreateHaircut