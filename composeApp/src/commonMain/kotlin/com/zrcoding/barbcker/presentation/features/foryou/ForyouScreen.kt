package com.zrcoding.barbcker.presentation.features.foryou

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.img_barber1
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Green700
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.collections.immutable.persistentListOf
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.ui.tooling.preview.Preview
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun ForYouRoute(
    viewModel: ForYouViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    ForYouScreen(viewState = viewState)
}

@Composable
private fun ForYouScreen(
    viewState: ForYouViewState,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(vertical = MaterialTheme.dimension.big)
            .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.bigger)
    ) {
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surfaceContainer, MaterialTheme.shapes.medium)
                .fillMaxWidth()
                .padding(MaterialTheme.dimension.default),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            Text(text = "Total revenue", style = MaterialTheme.typography.bodyLarge)
            Text(
                text = viewState.totalRevenue,
                style = MaterialTheme.typography.titleLarge,
                color = Green700
            )
        }
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
        ) {
            Text(text = "Barbers revenue", style = MaterialTheme.typography.titleMedium)
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
            ) {
                items(viewState.barbersRevenue) {
                    BarberRevenueItem(it)
                }
            }
        }
    }
}

@Composable
private fun BarberRevenueItem(barberRevenue: BarberRevenueUiModel) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier.size(55.dp).clip(CircleShape),
                painter = painterResource(Res.drawable.img_barber1),
                contentDescription = "Barber avatar"
            )
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
            ) {
                Text(text = barberRevenue.barberName, style = MaterialTheme.typography.bodyLarge)
                Text(
                    text = "${barberRevenue.totalHaircuts} Haircuts",
                    style = MaterialTheme.typography.labelMedium
                )
            }
        }
        Text(
            text = barberRevenue.totalRevenue,
            style = MaterialTheme.typography.titleMedium,
            color = Green700
        )
    }
}

@Preview
@Composable
private fun ForYouScreenPreview() {
    BarbckerTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            ForYouScreen(
                viewState = ForYouViewState(
                    totalRevenue = "150.0 $",
                    barbersRevenue = persistentListOf(
                        BarberRevenueUiModel(
                            barberId = "1",
                            barberName = "John",
                            totalHaircuts = 10,
                            totalRevenue = "100.0 $",
                            totalTips = "50.0 $"
                        ),
                        BarberRevenueUiModel(
                            barberId = "2",
                            barberName = "Alex",
                            totalHaircuts = 10,
                            totalRevenue = "100.0 $",
                            totalTips = "50.0$"
                        ),
                        BarberRevenueUiModel(
                            barberId = "3",
                            barberName = "Philipp",
                            totalHaircuts = 10,
                            totalRevenue = "100.0 $",
                            totalTips = "50.0 $"
                        )
                    )
                )
            )
        }
    }
}