package com.zrcoding.barbcker.presentation.features.barber.list

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.cash.paging.cachedIn
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import kotlinx.coroutines.launch

class BarbersListViewModel(
    private val barberRepository: BarberRepository,
) : ViewModel() {

    val barbers = barberRepository.observeAll().cachedIn(viewModelScope)

    fun onDeleteBarber(barber: <PERSON>) {
        viewModelScope.launch {
            barberRepository.delete(barber.uuid)
        }
    }
}