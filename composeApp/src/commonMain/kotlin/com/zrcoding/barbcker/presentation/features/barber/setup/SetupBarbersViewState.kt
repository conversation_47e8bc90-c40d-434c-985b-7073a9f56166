package com.zrcoding.barbcker.presentation.features.barber.setup

import androidx.compose.runtime.Stable
import app.cash.paging.PagingData
import com.zrcoding.barbcker.domain.models.Barber
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

@Stable
data class SetupBarbersViewState(
    val barbers: Flow<PagingData<Barber>> = flowOf(PagingData.empty()),
    val isSubmitting: Boolean = false,
)

sealed interface SetupBarbersOneTimeEvents {
    data object NavigateToHome : SetupBarbersOneTimeEvents
}