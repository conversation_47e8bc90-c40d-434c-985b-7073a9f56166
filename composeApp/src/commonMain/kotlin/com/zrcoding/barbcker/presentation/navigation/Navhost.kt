package com.zrcoding.barbcker.presentation.navigation

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.window.DialogProperties
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.compose.rememberNavController
import com.zrcoding.barbcker.presentation.features.app.StartDestination
import com.zrcoding.barbcker.presentation.features.auth.AuthRoute
import com.zrcoding.barbcker.presentation.features.barber.setup.SetupBarbersRoute
import com.zrcoding.barbcker.presentation.features.barber.upsert.UpsertBarberBottomSheet
import com.zrcoding.barbcker.presentation.features.complete_account.CompleteAccountRoute
import com.zrcoding.barbcker.presentation.features.haircut.CreateHaircutRoute
import com.zrcoding.barbcker.presentation.features.home.HomeScreen
import com.zrcoding.barbcker.presentation.features.home.rememberAppState
import com.zrcoding.barbcker.presentation.features.onboarding.OnboardingScreen

@Composable
fun BarbckerNavHost(
    modifier: Modifier = Modifier,
    startDestination: StartDestination,
) {
    val navController = rememberNavController()
    NavHost(
        navController = navController,
        startDestination = when (startDestination) {
            StartDestination.Onboarding -> Onboarding
            StartDestination.Auth -> Auth
            else -> Home
        },
        modifier = modifier
    ) {
        composableWithAnimation<Onboarding> {
            OnboardingScreen(
                onNextClick = { navController.navigate(Auth) }
            )
        }
        composableWithAnimation<Auth> {
            AuthRoute(
                navigateToCompleteAccount = {
                    navController.navigate(CompleteAccount)
                },
                navigateToSetupBarbers = {
                    navController.navigate(SetupBarbers)
                },
                navigateToHome = {
                    navController.navigate(Home) {
                        popUpTo(Auth) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        composableWithAnimation<CompleteAccount> {
            CompleteAccountRoute(
                navigateBack = { navController.popBackStack() },
                navigateToSetupBarbers = {
                    navController.navigate(SetupBarbers)
                },
                navigateToAuth = {
                    navController.popBackStack()
                }
            )
        }
        composableWithAnimation<SetupBarbers> {
            SetupBarbersRoute(
                navigateBack = { navController.popBackStack() },
                navigateToUpsertBarber = {
                    navController.navigate(UpsertBarber(barberId = it?.uuid))
                },
                navigateToHome = {
                    navController.navigate(Home) {
                        popUpTo(SetupBarbers) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        dialog<UpsertBarber> {
            UpsertBarberBottomSheet(
                onSuccess = {
                    navController.popBackStack()
                }
            )
        }
        composableWithAnimation<Home> {
            val appState = rememberAppState(navController = navController)
            HomeScreen(
                appState = appState,
                navigateToUpsertBarber = {
                    navController.navigate(UpsertBarber(barberId = it))
                }
            )
        }
        dialog<CreateHaircut>(
            dialogProperties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false,
            )
        ) {
            CreateHaircutRoute(
                navigateBack = { navController.popBackStack() },
            )
        }
    }
}

private inline fun <reified T : Any> NavGraphBuilder.composableWithAnimation(
    duration: Int = 400,
    noinline composable: @Composable AnimatedContentScope.(NavBackStackEntry) -> Unit
) {
    composable<T>(
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                animationSpec = tween(duration)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                animationSpec = tween(duration)
            )
        }
    ) {
        composable(it)
    }
}