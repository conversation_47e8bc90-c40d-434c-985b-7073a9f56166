package com.zrcoding.barbcker.presentation.features.barber.upsert

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.upsert_barber_commission_rate_invalid
import barbcker.composeapp.generated.resources.upsert_barber_commission_rate_required
import barbcker.composeapp.generated.resources.upsert_barber_name_required
import barbcker.composeapp.generated.resources.upsert_barber_phone_number_required
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import com.zrcoding.barbcker.presentation.navigation.UpsertBarber
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class UpsertBarberViewModel(
    private val barberRepository: BarberRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _viewState = MutableStateFlow(UpsertBarberViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<UpsertBarberSuccessEvent>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private val barberId = savedStateHandle.toRoute<UpsertBarber>().barberId

    init {
        if (barberId != null) {
            getBarberForEdit(barberId)
        }
    }

    fun getBarberForEdit(barberId: String) {
        viewModelScope.launch {
            barberRepository.getOne(barberId)?.let { barber ->
                _viewState.value = UpsertBarberViewState(
                    name = barber.name,
                    commissionRate = barber.commissionRate,
                    phoneNumber = barber.phoneNumber,
                )
            }
        }
    }

    fun onNameChanged(name: String) {
        _viewState.value = viewState.value.copy(name = name, nameError = null)
    }

    fun onCommissionRateChanged(commissionRate: Int?) {
        _viewState.value = viewState.value.copy(
            commissionRate = commissionRate,
            commissionRateError = null
        )
    }

    fun onCanCommissionRateBeZeroChanged(canCommissionRateBeZero: Boolean) {
        _viewState.value = viewState.value.copy(
            canCommissionRateBeZero = canCommissionRateBeZero,
            commissionRateError = null
        )
    }

    fun onPhoneNumberChanged(phoneNumber: String) {
        _viewState.value = viewState.value.copy(phoneNumber = phoneNumber, phoneNumberError = null)
    }

    fun onSubmit() {
        val (name, commissionRate, canCommissionRateBeZero, phoneNumber) = viewState.value
        if (name.isBlank()) {
            _viewState.value = viewState.value.copy(
                nameError = Res.string.upsert_barber_name_required
            )
            return
        }
        if (commissionRate == null) {
            if (!canCommissionRateBeZero) {
                _viewState.value = viewState.value.copy(
                    commissionRateError = Res.string.upsert_barber_commission_rate_required
                )
                return
            }
        } else {
            if (commissionRate < 0 || commissionRate > 100) {
                _viewState.value = viewState.value.copy(
                    commissionRateError = Res.string.upsert_barber_commission_rate_invalid
                )
                return
            }
        }
        if (phoneNumber.isBlank()) {
            _viewState.value = viewState.value.copy(
                phoneNumberError = Res.string.upsert_barber_phone_number_required
            )
            return
        }
        viewModelScope.launch {
            _viewState.update { it.copy(isSubmitting = true) }
            if (barberId != null) {
                barberRepository.update(
                    uuid = barberId,
                    name = name,
                    commissionRate = commissionRate ?: 0,
                    phoneNumber = phoneNumber
                )
            } else {
                barberRepository.insert(
                    name = name,
                    commissionRate = commissionRate ?: 0,
                    phoneNumber = phoneNumber
                )
            }
            _viewState.update { it.copy(isSubmitting = false) }
            _oneTimeEvents.emit(UpsertBarberSuccessEvent)
        }
    }
}