package com.zrcoding.barbcker.presentation.design_system.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.ArrowDropUp
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.create_haircut_barber_label
import barbcker.composeapp.generated.resources.create_haircut_barber_placeholder
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.theme.Blue500
import com.zrcoding.barbcker.presentation.design_system.theme.White
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.stringResource
import org.jetbrains.compose.ui.tooling.preview.Preview

@Stable
data class DropdownData(val id: String, val name: String, val suffix: String? = null)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> BcDropdown(
    items: PersistentList<T>,
    selectedItem: T?,
    key: (T) -> String,
    name: (T) -> String,
    onItemSelected: (T) -> Unit,
    modifier: Modifier = Modifier,
    title: String? = null,
    placeholder: StringResource? = null,
    required: Boolean = false,
    error: String? = null,
    colors: TextFieldColors = bcReadOnlyDisabledTextFieldColors,
) {
    // Controls dropdown visibility
    var expanded by remember { mutableStateOf(false) }
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
    ) {
        title?.let {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                if (required) {
                    Text(
                        text = "*",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                }
                Text(
                    text = it,
                    style = MaterialTheme.typography.titleSmall,
                )
            }
        }
        ExposedDropdownMenuBox(
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            expanded = expanded,
            onExpandedChange = { expanded = it }
        ) {
            BcTextField(
                modifier = Modifier.fillMaxWidth().menuAnchor(MenuAnchorType.PrimaryNotEditable),
                value = selectedItem?.let { name(it) }.orEmpty(),
                onValueChanged = {},
                readOnly = true,
                enabled = false,
                placeholder = placeholder,
                trailingIcon = {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.dimension.default),
                        imageVector = if (expanded) Icons.Default.ArrowDropUp else Icons.Default.ArrowDropDown,
                        contentDescription = null,
                    )
                },
                colors = colors,
            )
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
            ) {
                val sizeOfOneItem by remember {
                    mutableStateOf(48.dp) //assuming height of one menu item 50dp
                }
                val dimension = MaterialTheme.dimension
                val screenHeight50 by remember {
                    val screenHeight = 800.dp
                    mutableStateOf(screenHeight / 2) //assuming the drop down menu anchor is in middle of the screen. This is the maximum height that popup menu can take.
                }
                val height by remember(items.size) {
                    val itemsSize = sizeOfOneItem * items.size
                    mutableStateOf(minOf(itemsSize, screenHeight50))
                }
                val width by remember { mutableStateOf(400.dp - dimension.screenPaddingHorizontal) }
                LazyColumn(
                    modifier = Modifier
                        .width(width)
                        .height(height),
                ) {
                    items(items, key = { key(it) }) { item ->
                        val selected = item == selectedItem
                        val bgColor = if (selected) {
                            Blue500
                        } else Color.Unspecified
                        DropdownMenuItem(
                            modifier = Modifier.background(bgColor),
                            text = {
                                val textColor = if (selected) {
                                    White
                                } else Color.Unspecified
                                Text(
                                    text = name(item),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = textColor
                                )
                            },
                            onClick = {
                                expanded = false
                                onItemSelected(item)
                            },
                        )
                    }
                }
            }
        }
        error?.let {
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.titleSmall,
            )
        }
    }
}

@Preview
@Composable
private fun BcDropdownPreview() {
    val items = remember {
        List(100) {
            DropdownData("$it", "item $it")
        }.toPersistentList()
    }
    var selectedItem by remember { mutableStateOf<DropdownData?>(null) }
    BarbckerTheme {
        Row(
            modifier = Modifier.fillMaxSize().background(White),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
        ) {
            BcDropdown(
                modifier = Modifier.weight(1f),
                items = items,
                selectedItem = selectedItem,
                key = { it.id },
                name = { it.name },
                title = stringResource(Res.string.create_haircut_barber_label),
                placeholder = Res.string.create_haircut_barber_placeholder,
                onItemSelected = { selectedItem = it }
            )
            BcDropdown(
                modifier = Modifier.weight(1f),
                items = items,
                selectedItem = selectedItem,
                key = { it.id },
                name = { it.name },
                title = stringResource(Res.string.create_haircut_barber_label),
                placeholder = Res.string.create_haircut_barber_placeholder,
                onItemSelected = { selectedItem = it }
            )
        }
    }
}