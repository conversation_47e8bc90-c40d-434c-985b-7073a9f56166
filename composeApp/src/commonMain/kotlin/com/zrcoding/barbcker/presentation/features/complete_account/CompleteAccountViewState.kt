package com.zrcoding.barbcker.presentation.features.complete_account

import androidx.compose.runtime.Stable
import com.zrcoding.barbcker.domain.models.Currency
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import org.jetbrains.compose.resources.StringResource

@Stable
data class CompleteAccountViewState(
    val shopName: String = "",
    val currency: Currency = Currency.EURO,
    val shopNameError: StringResource? = null,
    val currencyError: StringResource? = null,
    val currencies: PersistentList<Currency> = Currency.entries.toPersistentList(),
)

sealed interface CompleteAccountOneTimeEvents {
    data object NavigateToSetupBarbers : CompleteAccountOneTimeEvents
    data object NavigateToAuth : CompleteAccountOneTimeEvents
}
