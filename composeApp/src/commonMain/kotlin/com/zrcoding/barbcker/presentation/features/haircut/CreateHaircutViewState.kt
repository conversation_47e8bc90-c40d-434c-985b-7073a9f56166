package com.zrcoding.barbcker.presentation.features.haircut

import androidx.compose.runtime.Stable
import com.zrcoding.barbcker.domain.models.Barber
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import org.jetbrains.compose.resources.StringResource

@Stable
data class CreateHaircutViewState(
    val barber: Barber? = null,
    val price: String = "",
    val tip: String = "",
    val barbers: PersistentList<Barber> = persistentListOf(),
    val receivedAmount: String = "",
    val commissionAmount: String = "",
    val barberError: StringResource? = null,
    val priceError: StringResource? = null,
    val isSubmitting: Boolean = false,
)

sealed interface CreateHaircutOneTimeEvents {
    data object Close : CreateHaircutOneTimeEvents
}
