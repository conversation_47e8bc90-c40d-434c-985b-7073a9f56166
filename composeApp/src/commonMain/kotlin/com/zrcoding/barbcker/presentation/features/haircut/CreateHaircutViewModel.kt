package com.zrcoding.barbcker.presentation.features.haircut

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.create_haircut_barber_required
import barbcker.composeapp.generated.resources.create_haircut_price_required
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import com.zrcoding.barbcker.domain.repositories.HaircutRepository
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class CreateHaircutViewModel(
    private val account: Account.Connected,
    private val barberRepository: BarberRepository,
    private val haircutRepository: HaircutRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(CreateHaircutViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvent = MutableSharedFlow<CreateHaircutOneTimeEvents>()
    val oneTimeEvent = _oneTimeEvent.asSharedFlow()

    init {
        viewModelScope.launch {
            val barbers = barberRepository.getAll().toPersistentList()
            _viewState.update { it.copy(barbers = barbers) }
        }
    }

    fun onBarberSelected(barber: Barber) {
        _viewState.update { it.copy(barber = barber, barberError = null) }
        _viewState.update {
            it.copy(
                receivedAmount = receivedAmount(it),
                commissionAmount = commissionAmount(it)
            )
        }
    }

    fun onPriceChanged(price: String) {
        _viewState.update { it.copy(price = price, priceError = null) }
        _viewState.update {
            it.copy(
                receivedAmount = receivedAmount(it),
                commissionAmount = commissionAmount(it)
            )
        }
    }

    fun onTipChanged(tip: String) {
        _viewState.update { it.copy(tip = tip) }
        _viewState.update {
            it.copy(
                receivedAmount = receivedAmount(it),
                commissionAmount = commissionAmount(it)
            )
        }
    }

    fun onSubmit() {
        val (barber, price, tip) = _viewState.value
        if (barber == null) {
            _viewState.update {
                it.copy(barberError = Res.string.create_haircut_barber_required)
            }
            return
        }
        if (price.toDoubleOrNull() == null || price.toDoubleOrNull() == 0.0) {
            _viewState.update {
                it.copy(priceError = Res.string.create_haircut_price_required)
            }
            return
        }
        viewModelScope.launch {
            haircutRepository.insert(
                barber = barber,
                price = price.toDouble(),
                tip = tip.toDoubleOrNull() ?: 0.0
            )
            _oneTimeEvent.emit(CreateHaircutOneTimeEvents.Close)
        }
    }

    private fun receivedAmount(viewState: CreateHaircutViewState): String {
        return with(viewState) {
            val amount = ((price.toDoubleOrNull() ?: 0.0) * (barber?.commissionRate ?: 0)) / 100
            amount.toString() + " " + account.currency?.symbol
        }
    }

    private fun commissionAmount(viewState: CreateHaircutViewState): String {
        return with(viewState) {
            val price = price.toDoubleOrNull() ?: 0.0
            val commission = 100 - (barber?.commissionRate ?: 100)
            val amount = (price * commission) / 100
            val tip = tip.toDoubleOrNull()
            buildString {
                append(amount)
                if (tip != null && tip > 0) {
                    append(" + ")
                    append(tip)
                }
                append(" ")
                append(account.currency?.symbol)
            }
        }
    }
}