package com.zrcoding.barbcker.presentation.features.foryou

import androidx.compose.runtime.Stable
import com.zrcoding.barbcker.domain.models.BarberRevenue
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf

@Stable
data class ForYouViewState(
    val totalRevenue: String = "",
    val barbersRevenue: PersistentList<BarberRevenueUiModel> = persistentListOf(),
)

@Stable
data class BarberRevenueUiModel(
    val barberId: String,
    val barberName: String,
    val totalHaircuts: Int,
    val totalRevenue: String,
    val totalTips: String,
)

fun BarberRevenue.toUiModel(currency: String?): BarberRevenueUiModel {
    return BarberRevenueUiModel(
        barberId = barber.uuid,
        barberName = barber.name,
        totalHaircuts = totalHaircuts,
        totalRevenue = "$totalRevenue $currency",
        totalTips = "$totalTips $currency"
    )
}
