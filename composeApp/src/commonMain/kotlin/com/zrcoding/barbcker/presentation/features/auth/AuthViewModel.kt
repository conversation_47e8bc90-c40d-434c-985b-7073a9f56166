package com.zrcoding.barbcker.presentation.features.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.AuthStatus
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import com.zrcoding.barbcker.presentation.common.extension.renderFailure
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AuthViewModel(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _viewState = MutableStateFlow(AuthViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<AuthOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    fun onGoogleBtnClicked() {
        viewModelScope.launch {
            val result = authRepository.authenticateWithGoogle()
            updateForResult(result)
        }
    }

    fun onAppleBtnClicked() {
        viewModelScope.launch {
            val result = authRepository.authenticateWithApple()
            updateForResult(result)
        }
    }

    private suspend fun updateForResult(result: Resource<AuthStatus, AuthErrors>) {
        when (result) {
            is Resource.Success -> {
                when (result.data) {
                    AuthStatus.COMPLETED -> _oneTimeEvents.emit(AuthOneTimeEvents.NavigateToHome)
                    AuthStatus.SHOULD_COMPLETE_ACCOUNT -> _oneTimeEvents.emit(
                        AuthOneTimeEvents.NavigateToCompleteAccount
                    )
                    AuthStatus.SHOULD_SETUP_BARBERS -> _oneTimeEvents.emit(
                        AuthOneTimeEvents.NavigateToSetupBarbers
                    )
                }
            }

            is Resource.Failure -> when (result.error) {
                is AuthErrors.Network -> renderFailure(result.error)
                is AuthErrors.ShouldRelogIn -> Unit
            }
        }
    }
}