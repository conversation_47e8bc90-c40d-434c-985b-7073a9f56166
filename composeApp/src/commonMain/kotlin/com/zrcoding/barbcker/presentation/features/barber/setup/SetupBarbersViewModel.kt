package com.zrcoding.barbcker.presentation.features.barber.setup

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class SetupBarbersViewModel(
    private val barberRepository: BarberRepository,
    private val accountRepository: AccountRepository,
) : ViewModel() {

    private val _viewState = MutableStateFlow(
        SetupBarbersViewState(barbers = barberRepository.observeAll())
    )
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<SetupBarbersOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()


    fun onDeleteBarber(barber: Barber) {
        viewModelScope.launch {
            barberRepository.delete(barber.uuid)
        }
    }

    fun onFinishClicked() {
        viewModelScope.launch {
            accountRepository.syncBarbers()
            _oneTimeEvents.emit(SetupBarbersOneTimeEvents.NavigateToHome)
        }
    }
}